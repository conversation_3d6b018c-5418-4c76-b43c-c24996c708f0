'use client';
import Image from 'next/image';
import { useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const ORANGE = "#e8561c";

export default function HorizontalScroll() {
  // Responsive font size function like in About.tsx
  const getResponsiveFontSize = (base: number) => {
    return `clamp(${base * 0.5}px, ${base * 0.08}vw, ${base}px)`;
  };

  const getResponsiveSpacing = (base: number) => {
    return `clamp(${base * 0.5}px, ${base * 0.1}vw, ${base}px)`;
  };

  useEffect(() => {
    let horizontalSections = gsap.utils.toArray(".container");

    horizontalSections.forEach((container: any) => {
      let sections = container.querySelectorAll(".panel");

      gsap.to(sections, {
        xPercent: -100 * (sections.length - 1),
        ease: "none",
        scrollTrigger: {
          trigger: container,
          pin: true,
          scrub: 1,
          end: "+=3500",
        }
      });
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <div>
      {/* Intro Section - styled like About.tsx */}
      <section
        style={{
          background: "#f0f0f0",
          width: "100%",
          minHeight: "100vh",
          fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          position: 'relative',
        }}
      >
        {/* Top label - WORK at very left of section */}
        <div style={{
          position: 'absolute',
          top: 120,
          left: 40,
          color: '#000',
          fontSize: 28,
          fontWeight: 700,
          letterSpacing: 1.5,
          zIndex: 10,
        }}>
          (WORK)
        </div>

        {/* Right label - 03 in orange */}
        <div style={{
          position: 'absolute',
          top: 'clamp(420px, 50vh, 580px)',
          right: 'clamp(20px, 3vw, 40px)',
          color: ORANGE,
          fontSize: getResponsiveFontSize(42),
          fontWeight: 700,
          letterSpacing: 1.5,
          zIndex: 10,
        }}>
          (03)
        </div>

        <div style={{
          maxWidth: 1200,
          margin: '0 auto',
          padding: 'clamp(20px, 3vw, 40px)',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          minHeight: '100vh',
          marginTop: 80,
          position: 'relative',
        }}>
          {/* Main content with responsive padding */}
          <div style={{ padding: '0' }}>
            {/* Intro text - same size as About.tsx */}
            <div style={{
              color: '#000',
              fontSize: getResponsiveFontSize(64),
              fontWeight: 700,
              margin: `${getResponsiveSpacing(180)} 0 ${getResponsiveSpacing(80)} 0`,
              maxWidth: 900,
              alignSelf: 'flex-start',
              lineHeight: 1.15,
            }}>
              A collection of projects that showcase my approach to solving complex problems through thoughtful design and clean code.
            </div>
          </div>
        </div>
      </section>

      {/* Horizontal Container */}
      <div
        className="container"
        style={{
          width: '600%',
          height: '100vh',
          display: 'flex',
          flexWrap: 'nowrap',
          overscrollBehavior: 'none'
        }}
      >
        <section className="panel w-full h-full bg-red-400 flex items-center justify-center">
          <Image
            src="/img/work/Screenshot 2025-06-28 164758.png"
            className="max-w-[80%] h-auto rounded-lg shadow-lg"
            width={600}
            height={400}
            alt="Project 1"
          />
        </section>
        <section className="panel w-full h-full bg-orange-400 flex items-center justify-center">
          <Image
            src="/img/work/Screenshot 2025-06-28 164452.png"
            className="max-w-[80%] h-auto rounded-lg shadow-lg"
            width={600}
            height={400}
            alt="Project 2"
          />
        </section>
        <section className="panel w-full h-full bg-purple-400 flex items-center justify-center">
          <Image
            src="/img/work/Screenshot 2025-06-28 164306.png"
            className="max-w-[80%] h-auto rounded-lg shadow-lg"
            width={600}
            height={400}
            alt="Project 3"
          />
        </section>
      </div>

      {/* First Vertical Section */}
      <section className="w-full h-screen bg-gray-800 flex items-center justify-center text-white">
        <h2 className="text-4xl font-bold">Vertical Section</h2>
      </section>
    </div>
  );
}
