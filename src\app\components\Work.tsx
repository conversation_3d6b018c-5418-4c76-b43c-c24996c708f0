'use client';
import Image from 'next/image';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

export default function HorizontalScroll() {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const container = containerRef.current;
    if (!container) return;

    const panels = container.querySelectorAll('.panel');
    if (panels.length === 0) return;

    // Only clear ScrollTriggers that belong to this component
    ScrollTrigger.getAll().forEach(trigger => {
      if (trigger.trigger === container) {
        trigger.kill();
      }
    });

    // Create horizontal scroll animation
    const tween = gsap.to(panels, {
      xPercent: -100 * (panels.length - 1),
      ease: "none",
      scrollTrigger: {
        trigger: container,
        pin: true,
        scrub: 1,
        start: "top top",
        end: () => "+=" + (container.scrollWidth - window.innerWidth),
        invalidateOnRefresh: true,
        id: "work-horizontal-scroll" // Give it a unique ID
      }
    });

    return () => {
      // Only kill this specific tween and its ScrollTrigger
      if (tween && tween.scrollTrigger) {
        tween.scrollTrigger.kill();
      }
      tween?.kill();
    };
  }, []);

  return (
    <div>
      {/* Intro Section */}
      <section
        className="w-full h-screen bg-white flex flex-col justify-center items-center text-black"
        style={{
          fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
          padding: '0 40px'
        }}
      >
        <div style={{
          maxWidth: '800px',
          textAlign: 'center'
        }}>
          <h1 style={{
            fontSize: '3.5rem',
            fontWeight: 700,
            lineHeight: 1.2,
            marginBottom: '24px',
            color: '#000'
          }}>
            Work
          </h1>
          <p style={{
            fontSize: '1.25rem',
            fontWeight: 400,
            lineHeight: 1.6,
            color: '#333',
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            A collection of projects that showcase my approach to solving complex problems through thoughtful design and clean code.
          </p>
        </div>
      </section>

      {/* Horizontal Panels */}
      <div ref={containerRef} className="flex h-screen overflow-hidden" style={{ width: '300%' }}>
        {/* Panel 1 */}
        <div className="panel min-w-full h-full bg-red-400 flex items-center justify-center">
          <div className="max-w-6xl mx-auto px-8 w-full">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center h-full">
              <div className="flex justify-center">
                <Image
                  src="/img/work/Screenshot 2025-06-28 164758.png"
                  className="max-w-full h-auto rounded-lg shadow-lg"
                  width={500}
                  height={300}
                  alt="Project 1"
                />
              </div>
              <div className="text-black">
                <h2 className="text-4xl font-bold mb-6">Panel 1</h2>
                <p className="text-lg">
                  Lorem Ipsum is simply dummy text of the printing and typesetting industry.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Panel 2 */}
        <div className="panel min-w-full h-full bg-orange-400 flex items-center justify-center">
          <div className="max-w-6xl mx-auto px-8 w-full">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center h-full">
              <div className="flex justify-center">
                <Image
                  src="/img/work/Screenshot 2025-06-28 164452.png"
                  className="max-w-full h-auto rounded-lg shadow-lg"
                  width={500}
                  height={300}
                  alt="Project 2"
                />
              </div>
              <div className="text-black">
                <h2 className="text-4xl font-bold mb-6">Panel 2</h2>
                <p className="text-lg">
                  Lorem Ipsum is simply dummy text of the printing and typesetting industry.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Panel 3 */}
        <div className="panel min-w-full h-full bg-purple-400 flex items-center justify-center">
          <div className="max-w-6xl mx-auto px-8 w-full">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center h-full">
              <div className="flex justify-center">
                <Image
                  src="/img/work/Screenshot 2025-06-28 164306.png"
                  className="max-w-full h-auto rounded-lg shadow-lg"
                  width={500}
                  height={300}
                  alt="Project 3"
                />
              </div>
              <div className="text-black">
                <h2 className="text-4xl font-bold mb-6">Panel 3</h2>
                <p className="text-lg">
                  Lorem Ipsum is simply dummy text of the printing and typesetting industry.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* End Section */}
      <section className="w-full h-screen bg-gray-800 flex items-center justify-center text-white">
        <h2 className="text-4xl font-bold">Ende</h2>
      </section>
    </div>
  );
}
