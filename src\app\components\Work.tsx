'use client';
import Image from 'next/image';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

export default function HorizontalScroll() {
  const containerRef = useRef();
  const sectionsRef = useRef([]);

  useEffect(() => {
    const sections = sectionsRef.current;

    if (sections.length > 0 && containerRef.current) {
      // Horizontale Scroll Animation
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: containerRef.current,
          pin: true,
          scrub: 1,
          snap: 1 / (sections.length - 1),
          end: () => "+=" + containerRef.current.scrollWidth
        }
      });

      tl.to(sections, {
        xPercent: -100 * (sections.length - 1),
        ease: "none"
      });

      // Text Animation für jede Section
      sections.forEach((section, i) => {
        const heading = section.querySelector('h2');
        if (heading) {
          gsap.fromTo(heading,
            { x: 300, opacity: 0.5 },
            {
              x: -300,
              opacity: 1,
              scrollTrigger: {
                trigger: section,
                start: "left 80%",
                end: "right 20%",
                scrub: true,
                containerAnimation: tl
              }
            }
          );
        }
      });
    }
  }, []);
  return (
    <main>
      <article>
        <header className="text-white relative w-full bg-slate-950 grid place-content-center h-[80vh]">
          <div className="absolute bottom-0 left-0 right-0 top-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:14px_24px] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)]"></div>

          <h1 className="text-6xl font-bold text-center tracking-tight">
            Meine Arbeit <br />
            Scroll & Entdecke
          </h1>
        </header>

        <div ref={containerRef} className="h-screen overflow-hidden">
          <div className="flex h-full">
            <section
              ref={el => sectionsRef.current[0] = el}
              className="min-w-full h-full bg-red-400 flex flex-col justify-center items-center"
            >
              <h2 className="text-[20vw] font-semibold text-black">
                PROJECT 1
              </h2>
              <div className="w-[400px] h-[300px] bg-white/20 rounded-lg flex items-center justify-center">
                <span className="text-2xl text-black">Dein Bild 1</span>
              </div>
            </section>

            <section
              ref={el => sectionsRef.current[1] = el}
              className="min-w-full h-full bg-blue-400 flex flex-col justify-center items-center"
            >
              <h2 className="text-[20vw] font-semibold text-black">
                PROJECT 2
              </h2>
              <div className="w-[400px] h-[300px] bg-white/20 rounded-lg flex items-center justify-center">
                <span className="text-2xl text-black">Dein Bild 2</span>
              </div>
            </section>

            <section
              ref={el => sectionsRef.current[2] = el}
              className="min-w-full h-full bg-orange-400 flex flex-col justify-center items-center"
            >
              <h2 className="text-[20vw] font-semibold text-black">
                PROJECT 3
              </h2>
              <div className="w-[400px] h-[300px] bg-white/20 rounded-lg flex items-center justify-center">
                <span className="text-2xl text-black">Dein Bild 3</span>
              </div>
            </section>

            <section
              ref={el => sectionsRef.current[3] = el}
              className="min-w-full h-full bg-green-400 flex flex-col justify-center items-center"
            >
              <h2 className="text-[20vw] font-semibold text-black">
                SPECIAL
              </h2>
              <div className="w-[400px] h-[300px] bg-white/20 rounded-lg flex items-center justify-center">
                <span className="text-2xl text-black">Dein Bild 4</span>
              </div>
            </section>
          </div>
        </div>

        <footer className="bg-slate-900 text-white grid place-content-center h-[60vh]">
          <div className="text-center">
            <h3 className="text-3xl font-bold mb-4">Mehr Projekte?</h3>
            <p className="text-lg opacity-80">
              Kontaktiere mich für weitere Arbeiten
            </p>
          </div>
        </footer>
      </article>
    </main>
  );
}
