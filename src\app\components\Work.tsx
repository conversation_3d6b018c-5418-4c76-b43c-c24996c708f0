'use client';
import Image from 'next/image';
import { useEffect, useRef } from 'react';
import { animate, scroll, spring } from 'motion';
import { ReactLenis } from 'lenis/react';

const ORANGE = "#e8561c";

export default function HorizontalScroll() {
  const ulRef = useRef();

  // Responsive font size function like in About.tsx
  const getResponsiveFontSize = (base: number) => {
    return `clamp(${base * 0.5}px, ${base * 0.08}vw, ${base}px)`;
  };

  const getResponsiveSpacing = (base: number) => {
    return `clamp(${base * 0.5}px, ${base * 0.1}vw, ${base}px)`;
  };

  useEffect(() => {
    const items = document.querySelectorAll('li');
    if (ulRef.current) {
      const controls = animate(
        ulRef.current,
        {
          transform: ['none', `translateX(-${items.length - 1}00vw)`],
        },
        { easing: spring() }
      );
      scroll(controls, { target: document.querySelector('section') });
    }
    const segmentLength = 1 / items.length;
    items.forEach((item, i) => {
      const header = item.querySelector('h2');
      if (header) {
        scroll(animate([header], { x: [800, -800] }), {
          target: document.querySelector('section'),
          offset: [
            [i * segmentLength, 1],
            [(i + 1) * segmentLength, 0],
          ],
        });
      }
    });
  }, []);

  return (
    <div>
      {/* Intro Section - styled like About.tsx */}
      <section
        style={{
          background: "#f0f0f0",
          width: "100%",
          minHeight: "100vh",
          fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          position: 'relative',
        }}
      >
        {/* Top label - WORK at very left of section */}
        <div style={{
          position: 'absolute',
          top: 120,
          left: 40,
          color: '#000',
          fontSize: 28,
          fontWeight: 700,
          letterSpacing: 1.5,
          zIndex: 10,
        }}>
          (WORK)
        </div>

        {/* Right label - 03 in orange */}
        <div style={{
          position: 'absolute',
          top: 'clamp(420px, 50vh, 580px)',
          right: 'clamp(20px, 3vw, 40px)',
          color: ORANGE,
          fontSize: getResponsiveFontSize(42),
          fontWeight: 700,
          letterSpacing: 1.5,
          zIndex: 10,
        }}>
          (03)
        </div>

        <div style={{
          maxWidth: 1200,
          margin: '0 auto',
          padding: 'clamp(20px, 3vw, 40px)',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          minHeight: '100vh',
          marginTop: 80,
          position: 'relative',
        }}>
          {/* Main content with responsive padding */}
          <div style={{ padding: '0' }}>
            {/* Intro text - same size as About.tsx */}
            <div style={{
              color: '#000',
              fontSize: getResponsiveFontSize(64),
              fontWeight: 700,
              margin: `${getResponsiveSpacing(180)} 0 ${getResponsiveSpacing(80)} 0`,
              maxWidth: 900,
              alignSelf: 'flex-start',
              lineHeight: 1.15,
            }}>
              A collection of projects that showcase my approach to solving complex problems through thoughtful design and clean code.
            </div>
          </div>
        </div>
      </section>

      {/* Motion Horizontal Scroll Section */}
      <ReactLenis root>
        <main>
          <article>
            <section className="h-[500vh] relative">
              <ul ref={ulRef} className="flex sticky top-0">
                <li className="h-screen w-screen bg-red-400 flex flex-col justify-center overflow-hidden items-center">
                  <h2 className="text-[20vw] font-semibold relative bottom-5 inline-block text-black">
                    PROJECT 1
                  </h2>
                  <Image
                    src="/img/work/Screenshot 2025-06-28 164758.png"
                    className="2xl:w-[550px] w-[380px] absolute bottom-0"
                    width={500}
                    height={500}
                    alt="Project 1"
                  />
                </li>
                <li className="h-screen w-screen bg-blue-400 flex flex-col justify-center overflow-hidden items-center">
                  <h2 className="text-[20vw] font-semibold relative bottom-5 inline-block text-black">
                    PROJECT 2
                  </h2>
                  <Image
                    src="/img/work/Screenshot 2025-06-28 164452.png"
                    className="2xl:w-[550px] w-[380px] absolute bottom-0"
                    width={500}
                    height={500}
                    alt="Project 2"
                  />
                </li>
                <li className="h-screen w-screen bg-orange-400 flex flex-col justify-center overflow-hidden items-center">
                  <h2 className="text-[20vw] font-semibold relative bottom-5 inline-block text-black">
                    PROJECT 3
                  </h2>
                  <Image
                    src="/img/work/Screenshot 2025-06-28 164306.png"
                    className="2xl:w-[550px] w-[380px] absolute bottom-0"
                    width={500}
                    height={500}
                    alt="Project 3"
                  />
                </li>
              </ul>
            </section>
            <footer className="bg-red-600 text-white grid place-content-center h-[80vh]">
              <p>
                Meine Projekte{' '}
                <a target="_blank" href="#">
                  Portfolio
                </a>
              </p>
            </footer>
          </article>
          <div className="progress fixed left-0 right-0 h-2 rounded-full bg-red-600 bottom-[50px] scale-x-0"></div>
        </main>
      </ReactLenis>
    </div>
  );
}
