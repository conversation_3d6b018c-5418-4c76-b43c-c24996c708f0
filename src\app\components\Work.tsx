'use client';
import Image from 'next/image';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

export default function HorizontalScroll() {
  const panelsContainerRef = useRef();

  useEffect(() => {
    const panels = gsap.utils.toArray(".panel");
    const panelsContainer = panelsContainerRef.current;

    if (panels.length > 0 && panelsContainer) {
      gsap.to(panels, {
        x: () => -1 * (panelsContainer.scrollWidth - window.innerWidth),
        ease: "none",
        scrollTrigger: {
          trigger: panelsContainer,
          pin: true,
          start: "top top",
          scrub: 1,
          end: () => "+=" + (panelsContainer.scrollWidth - window.innerWidth)
        }
      });
    }
  }, []);
  return (
    <div className="site">
      <main className="site-content">
        {/* Intro Section */}
        <section className="w-full h-screen bg-blue-500 flex flex-col justify-center items-center text-white overflow-hidden">
          <h1 className="text-6xl font-bold text-center">
            Meine Arbeit <br />
            Scroll & Entdecke
          </h1>
        </section>

        {/* Panels Section */}
        <section id="panels">
          <div
            ref={panelsContainerRef}
            id="panels-container"
            className="flex h-screen overflow-hidden"
            style={{ width: '300%' }}
          >
            {/* Panel 1 */}
            <article className="panel w-full h-full bg-red-400 flex items-center">
              <div className="container mx-auto px-8">
                <div className="grid grid-cols-2 gap-8 h-full items-center">
                  <div className="flex justify-center">
                    <Image
                      src="/img/work/Screenshot 2025-06-28 164758.png"
                      className="max-w-full h-auto rounded-lg shadow-lg"
                      width={600}
                      height={400}
                      alt="Project 1"
                    />
                  </div>
                  <div className="flex flex-col justify-center text-black">
                    <h2 className="text-4xl font-bold mb-6">Panel 1</h2>
                    <p className="text-lg mb-8">
                      Lorem Ipsum is simply dummy text of the printing and typesetting industry. Including versions of Lorem Ipsum.
                    </p>
                  </div>
                </div>
              </div>
            </article>

            {/* Panel 2 */}
            <article className="panel w-full h-full bg-orange-400 flex items-center">
              <div className="container mx-auto px-8">
                <div className="grid grid-cols-2 gap-8 h-full items-center">
                  <div className="flex justify-center">
                    <Image
                      src="/img/work/Screenshot 2025-06-28 164452.png"
                      className="max-w-full h-auto rounded-lg shadow-lg"
                      width={600}
                      height={400}
                      alt="Project 2"
                    />
                  </div>
                  <div className="flex flex-col justify-center text-black">
                    <h2 className="text-4xl font-bold mb-6">Panel 2</h2>
                    <p className="text-lg mb-8">
                      Lorem Ipsum is simply dummy text of the printing and typesetting industry. Including versions of Lorem Ipsum.
                    </p>
                  </div>
                </div>
              </div>
            </article>

            {/* Panel 3 */}
            <article className="panel w-full h-full bg-purple-400 flex items-center">
              <div className="container mx-auto px-8">
                <div className="grid grid-cols-2 gap-8 h-full items-center">
                  <div className="flex justify-center">
                    <Image
                      src="/img/work/Screenshot 2025-06-28 164306.png"
                      className="max-w-full h-auto rounded-lg shadow-lg"
                      width={600}
                      height={400}
                      alt="Project 3"
                    />
                  </div>
                  <div className="flex flex-col justify-center text-black">
                    <h2 className="text-4xl font-bold mb-6">Panel 3</h2>
                    <p className="text-lg mb-8">
                      Lorem Ipsum is simply dummy text of the printing and typesetting industry. Including versions of Lorem Ipsum.
                    </p>
                  </div>
                </div>
              </div>
            </article>
          </div>
        </section>

        {/* Map Section */}
        <section className="w-full h-screen bg-gray-800 flex items-center justify-center text-white overflow-hidden">
          <h2 className="text-4xl font-bold">Map Section</h2>
        </section>
      </main>
    </div>
  );
}
