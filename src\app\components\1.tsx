'use client';
import Image from 'next/image';
import { useEffect, useRef } from 'react';
import { animate, scroll, spring } from 'motion';
import { ReactLenis } from 'lenis/react';

const images = [
  '/img/work/Screenshot 2025-06-28 164306.png',
  '/img/work/Screenshot 2025-06-28 164452.png',
  '/img/work/Screenshot 2025-06-28 164758.png',
  '/img/WhatsApp Bild 2025-04-30 um 09.07.40_12fb4f2a.jpg',
];

export default function Work() {
  const ulRef = useRef<HTMLUListElement>(null);
  useEffect(() => {
    const items = document.querySelectorAll('li');
    if (ulRef.current) {
      const controls = animate(
        ulRef.current,
        {
          transform: ['none', `translateX(-${(items.length - 1)}00vw)`],
        },
        { easing: spring() }
      );
      scroll(controls, { target: document.querySelector('section.work-horizontal') });
    }
    const segmentLength = 1 / items.length;
    items.forEach((item, i) => {
      const header = item.querySelector('h2');
      scroll(animate([header], { x: [800, -800] }), {
        target: document.querySelector('section.work-horizontal'),
        offset: [
          [i * segmentLength, 1],
          [(i + 1) * segmentLength, 0],
        ],
      });
    });
  }, []);
  return (
    <ReactLenis root>
      <section className="work-horizontal h-[500vh] relative bg-[#f0f0f0]">
        <ul ref={ulRef} className="flex sticky top-0">
          <li className="h-screen w-screen bg-[#ededed] flex flex-col justify-center overflow-hidden items-center">
            <h2 className="text-[20vw] font-semibold relative bottom-5 inline-block text-black">
              PASSION
            </h2>
            <Image
              src={images[0]}
              className="2xl:w-[550px] w-[380px] absolute bottom-0"
              width={500}
              height={500}
              alt="image"
              priority
            />
          </li>
          <li className="h-screen w-screen bg-[#e8561c] flex flex-col justify-center overflow-hidden items-center">
            <h2 className="text-[20vw] font-semibold relative bottom-5 inline-block text-white">
              WORK
            </h2>
            <Image
              src={images[1]}
              className="2xl:w-[550px] w-[380px] absolute bottom-0"
              width={500}
              height={500}
              alt="image"
              priority
            />
          </li>
          <li className="h-screen w-screen bg-[#f0f0f0] flex flex-col justify-center overflow-hidden items-center">
            <h2 className="text-[20vw] font-semibold relative bottom-5 inline-block text-black">
              MOTIVATION
            </h2>
            <Image
              src={images[2]}
              className="2xl:w-[550px] w-[380px] absolute bottom-0"
              width={500}
              height={500}
              alt="image"
              priority
            />
          </li>
          <li className="h-screen w-screen bg-[#ededed] flex flex-col justify-center overflow-hidden items-center">
            <h2 className="text-[20vw] font-semibold relative bottom-5 inline-block text-black">
              INSPIRATION
            </h2>
            <Image
              src={images[3]}
              className="2xl:w-[550px] w-[380px] absolute bottom-0"
              width={500}
              height={500}
              alt="image"
              priority
            />
          </li>
        </ul>
      </section>
    </ReactLenis>
  );
}
